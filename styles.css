/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  background-color: #0a0a0a;
  color: #ffffff;
  line-height: 1.6;
  overflow-x: hidden;
}

/* Image Placeholders */
.image-placeholder {
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  border: 2px dashed #333333;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.image-placeholder::after {
  content: 'Image Placeholder';
  color: #808080;
  font-size: 14px;
  font-weight: 500;
}

.logo-placeholder {
  width: 120px;
  height: 40px;
  background: linear-gradient(135deg, #0066ff 0%, #00d4ff 100%);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-placeholder::after {
  content: 'LOGO';
  color: white;
  font-size: 12px;
  font-weight: 700;
}

/* Buttons */
.cta-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.cta-button.primary {
  background: linear-gradient(135deg, #0066ff 0%, #00d4ff 100%);
  color: white;
}

.cta-button.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 102, 255, 0.3);
}

.cta-button.secondary {
  background: transparent;
  color: #ffffff;
  border: 2px solid #333333;
}

.cta-button.secondary:hover {
  border-color: #0066ff;
  color: #0066ff;
}

.cta-button.large {
  padding: 16px 32px;
  font-size: 18px;
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(10, 10, 10, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 15px 0;
  border-bottom: 1px solid #333333;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-menu {
  display: flex;
  gap: 30px;
}

.nav-link {
  color: #b0b0b0;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #0066ff;
}

.nav-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
}

.nav-toggle span {
  width: 25px;
  height: 3px;
  background: #ffffff;
  margin: 3px 0;
  transition: 0.3s;
}

/* Hero Section */
.hero {
  padding: 120px 0 80px;
  background: radial-gradient(
    ellipse at center,
    rgba(0, 102, 255, 0.1) 0%,
    transparent 70%
  );
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #ffffff 0%, #00d4ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 18px;
  color: #b0b0b0;
  margin-bottom: 30px;
  max-width: 500px;
}

.hero-image .image-placeholder {
  width: 100%;
  height: 400px;
}

/* Stats Section */
.stats {
  padding: 60px 0;
  background: #1a1a1a;
}

.stats-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 3rem;
  font-weight: 800;
  color: #0066ff;
  margin-bottom: 10px;
}

.stat-label {
  color: #b0b0b0;
  font-size: 16px;
}

/* Features Section */
.features {
  padding: 80px 0;
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.feature-card {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.feature-image .image-placeholder {
  width: 100%;
  height: 350px;
}

.feature-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 20px;
}

.feature-description {
  color: #b0b0b0;
  font-size: 18px;
  margin-bottom: 30px;
  line-height: 1.7;
}

/* Services Section */
.services {
  padding: 80px 0;
  background: #1a1a1a;
}

.services-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.service-card {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.service-image .image-placeholder {
  width: 100%;
  height: 350px;
}

.service-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 20px;
}

.service-description {
  color: #b0b0b0;
  font-size: 18px;
  margin-bottom: 30px;
  line-height: 1.7;
}

/* Process Section */
.process {
  padding: 80px 0;
}

.process-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 60px;
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
}

.step {
  text-align: center;
  padding: 30px 20px;
  background: #1a1a1a;
  border-radius: 12px;
  border: 1px solid #333333;
  transition: transform 0.3s ease;
}

.step:hover {
  transform: translateY(-5px);
}

.step-number {
  font-size: 3rem;
  font-weight: 800;
  color: #0066ff;
  margin-bottom: 15px;
}

.step-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 15px;
}

.step-description {
  color: #b0b0b0;
  font-size: 14px;
  line-height: 1.6;
}

/* Results Section */
.results {
  padding: 80px 0;
  background: #1a1a1a;
}

.results-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.results-description {
  color: #b0b0b0;
  font-size: 18px;
  line-height: 1.7;
  margin-top: 20px;
}

.results-chart .image-placeholder {
  width: 100%;
  height: 300px;
}

.results-chart .image-placeholder.chart-img::after {
  content: 'Analytics Chart';
}

/* Testimonials Section */
.testimonials {
  padding: 80px 0;
}

.testimonials-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.testimonial-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
}

.testimonial-card {
  background: #1a1a1a;
  padding: 30px;
  border-radius: 12px;
  border: 1px solid #333333;
}

.testimonial-text {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 20px;
  color: #b0b0b0;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 15px;
}

.author-avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #0066ff 0%, #00d4ff 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.author-avatar::after {
  content: '👤';
  font-size: 20px;
}

.author-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 5px;
}

.author-title {
  font-size: 14px;
  color: #808080;
}

/* Final CTA Section */
.final-cta {
  padding: 80px 0;
  background: linear-gradient(
    135deg,
    rgba(0, 102, 255, 0.1) 0%,
    rgba(0, 212, 255, 0.1) 100%
  );
  text-align: center;
}

.cta-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 20px;
}

.cta-description {
  color: #b0b0b0;
  font-size: 18px;
  margin-bottom: 40px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Footer */
.footer {
  background: #000000;
  padding: 40px 0 20px;
  border-top: 1px solid #333333;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30px;
}

.footer-links {
  display: flex;
  gap: 30px;
}

.footer-link {
  color: #b0b0b0;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: #0066ff;
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #333333;
}

.footer-bottom p {
  color: #808080;
  font-size: 14px;
}

/* Responsive Design */
@media (min-width: 1440px) {
  .nav-container,
  .hero-container,
  .stats-container,
  .features-container,
  .services-container,
  .process-container,
  .results-container,
  .testimonials-container,
  .cta-container,
  .footer-container {
    max-width: 1400px;
    padding: 0 40px;
  }
}

@media (max-width: 1024px) {
  .hero-container,
  .feature-card,
  .service-card,
  .results-container {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .service-card .service-content {
    order: initial;
  }

  .process-steps {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Mobile Navigation */
@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 70px);
    background: rgba(10, 10, 10, 0.98);
    backdrop-filter: blur(10px);
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: 50px;
    transition: left 0.3s ease;
    z-index: 999;
  }

  .nav-menu.active {
    left: 0;
    display: flex;
  }

  .nav-menu .nav-link {
    font-size: 18px;
    margin: 15px 0;
  }

  .nav-toggle {
    display: flex;
  }

  .nav-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }

  .nav-toggle.active span:nth-child(2) {
    opacity: 0;
  }

  .nav-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }

  .hero {
    padding: 100px 0 60px;
  }

  .hero-image .image-placeholder {
    height: 300px;
  }

  .stats-container {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .section-title {
    font-size: 2rem;
    margin-bottom: 40px;
  }

  .process-steps {
    grid-template-columns: 1fr;
  }

  .testimonial-grid {
    grid-template-columns: 1fr;
  }

  .footer-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .cta-title {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .feature-title,
  .service-title,
  .section-title {
    font-size: 1.75rem;
  }

  .hero-subtitle,
  .feature-description,
  .service-description,
  .results-description {
    font-size: 16px;
  }

  .footer-links {
    flex-direction: column;
    gap: 15px;
  }

  .nav-container,
  .hero-container,
  .stats-container,
  .features-container,
  .services-container,
  .process-container,
  .results-container,
  .testimonials-container,
  .cta-container,
  .footer-container {
    padding: 0 15px;
  }
}

/* Animation Classes */
.animate-in {
  animation: fadeInUp 0.8s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Ripple Effect for Buttons */
.cta-button {
  position: relative;
  overflow: hidden;
}

.ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation: ripple-animation 0.6s linear;
  pointer-events: none;
}

@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* Navbar Scroll Effect */
.navbar.scrolled {
  background: rgba(0, 0, 0, 0.95);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
}

/* Hover Effects */
.step:hover,
.testimonial-card:hover {
  box-shadow: 0 10px 30px rgba(0, 102, 255, 0.1);
}

/* Loading States for Image Placeholders */
.image-placeholder {
  position: relative;
  overflow: hidden;
}

.image-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Focus Styles for Accessibility */
.cta-button:focus,
.nav-link:focus,
.nav-toggle:focus {
  outline: 2px solid #0066ff;
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  .navbar,
  .nav-toggle,
  .cta-button {
    display: none;
  }

  body {
    background: white;
    color: black;
  }

  .hero,
  .stats,
  .features,
  .services,
  .process,
  .results,
  .testimonials,
  .final-cta {
    background: white;
    color: black;
  }
}
