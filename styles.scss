// Variables
$primary-color: #0066ff;
$secondary-color: #00d4ff;
$dark-bg: #0a0a0a;
$darker-bg: #000000;
$card-bg: #1a1a1a;
$text-primary: #ffffff;
$text-secondary: #b0b0b0;
$text-muted: #808080;
$border-color: #333333;

// Breakpoints
$mobile: 480px;
$tablet: 768px;
$desktop: 1024px;
$large-desktop: 1440px;

// Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  
  @media (min-width: $large-desktop) {
    max-width: 1400px;
    padding: 0 40px;
  }
}

@mixin button-base {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

// Reset and Base Styles
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  background-color: $dark-bg;
  color: $text-primary;
  line-height: 1.6;
  overflow-x: hidden;
}

// Image Placeholders
.image-placeholder {
  background: linear-gradient(135deg, $card-bg 0%, #2a2a2a 100%);
  border: 2px dashed $border-color;
  border-radius: 8px;
  @include flex-center;
  position: relative;
  
  &::after {
    content: "Image Placeholder";
    color: $text-muted;
    font-size: 14px;
    font-weight: 500;
  }
}

.logo-placeholder {
  width: 120px;
  height: 40px;
  background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
  border-radius: 6px;
  
  &::after {
    content: "LOGO";
    color: white;
    font-size: 12px;
    font-weight: 700;
  }
}

// Buttons
.cta-button {
  @include button-base;
  
  &.primary {
    background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
    color: white;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(0, 102, 255, 0.3);
    }
  }
  
  &.secondary {
    background: transparent;
    color: $text-primary;
    border: 2px solid $border-color;
    
    &:hover {
      border-color: $primary-color;
      color: $primary-color;
    }
  }
  
  &.large {
    padding: 16px 32px;
    font-size: 18px;
  }
}

// Navigation
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(10, 10, 10, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 15px 0;
  border-bottom: 1px solid $border-color;
}

.nav-container {
  @include container;
  @include flex-between;
}

.nav-menu {
  display: flex;
  gap: 30px;
  
  @media (max-width: $tablet) {
    display: none;
  }
}

.nav-link {
  color: $text-secondary;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
  
  &:hover {
    color: $primary-color;
  }
}

.nav-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  
  @media (max-width: $tablet) {
    display: flex;
  }
  
  span {
    width: 25px;
    height: 3px;
    background: $text-primary;
    margin: 3px 0;
    transition: 0.3s;
  }
}

// Hero Section
.hero {
  padding: 120px 0 80px;
  background: radial-gradient(ellipse at center, rgba(0, 102, 255, 0.1) 0%, transparent 70%);
}

.hero-container {
  @include container;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  
  @media (max-width: $desktop) {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 20px;
  background: linear-gradient(135deg, $text-primary 0%, $secondary-color 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 18px;
  color: $text-secondary;
  margin-bottom: 30px;
  max-width: 500px;
}

.hero-image {
  .image-placeholder {
    width: 100%;
    height: 400px;
    
    @media (max-width: $tablet) {
      height: 300px;
    }
  }
}

// Stats Section
.stats {
  padding: 60px 0;
  background: $card-bg;
}

.stats-container {
  @include container;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  
  @media (max-width: $tablet) {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 3rem;
  font-weight: 800;
  color: $primary-color;
  margin-bottom: 10px;
}

.stat-label {
  color: $text-secondary;
  font-size: 16px;
}

// Features Section
.features {
  padding: 80px 0;
}

.features-container {
  @include container;
}

.feature-card {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  
  @media (max-width: $desktop) {
    grid-template-columns: 1fr;
    gap: 40px;
  }
}

.feature-image {
  .image-placeholder {
    width: 100%;
    height: 350px;
  }
}

.feature-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 20px;
}

.feature-description {
  color: $text-secondary;
  font-size: 18px;
  margin-bottom: 30px;
  line-height: 1.7;
}

// Services Section
.services {
  padding: 80px 0;
  background: $card-bg;
}

.services-container {
  @include container;
}

.service-card {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  
  @media (max-width: $desktop) {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  @media (min-width: $desktop) {
    .service-content {
      order: -1;
    }
  }
}

.service-image {
  .image-placeholder {
    width: 100%;
    height: 350px;
  }
}

.service-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 20px;
}

.service-description {
  color: $text-secondary;
  font-size: 18px;
  margin-bottom: 30px;
  line-height: 1.7;
}

// Process Section
.process {
  padding: 80px 0;
}

.process-container {
  @include container;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 60px;

  @media (max-width: $tablet) {
    font-size: 2rem;
    margin-bottom: 40px;
  }
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;

  @media (max-width: $desktop) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: $tablet) {
    grid-template-columns: 1fr;
  }
}

.step {
  text-align: center;
  padding: 30px 20px;
  background: $card-bg;
  border-radius: 12px;
  border: 1px solid $border-color;
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-5px);
  }
}

.step-number {
  font-size: 3rem;
  font-weight: 800;
  color: $primary-color;
  margin-bottom: 15px;
}

.step-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 15px;
}

.step-description {
  color: $text-secondary;
  font-size: 14px;
  line-height: 1.6;
}

// Results Section
.results {
  padding: 80px 0;
  background: $card-bg;
}

.results-container {
  @include container;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;

  @media (max-width: $desktop) {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
}

.results-description {
  color: $text-secondary;
  font-size: 18px;
  line-height: 1.7;
  margin-top: 20px;
}

.results-chart {
  .image-placeholder {
    width: 100%;
    height: 300px;

    &.chart-img::after {
      content: "Analytics Chart";
    }
  }
}

// Testimonials Section
.testimonials {
  padding: 80px 0;
}

.testimonials-container {
  @include container;
}

.testimonial-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;

  @media (max-width: $tablet) {
    grid-template-columns: 1fr;
  }
}

.testimonial-card {
  background: $card-bg;
  padding: 30px;
  border-radius: 12px;
  border: 1px solid $border-color;
}

.testimonial-text {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 20px;
  color: $text-secondary;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 15px;
}

.author-avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
  border-radius: 50%;
  @include flex-center;

  &::after {
    content: "👤";
    font-size: 20px;
  }
}

.author-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 5px;
}

.author-title {
  font-size: 14px;
  color: $text-muted;
}

// Final CTA Section
.final-cta {
  padding: 80px 0;
  background: linear-gradient(135deg, rgba(0, 102, 255, 0.1) 0%, rgba(0, 212, 255, 0.1) 100%);
  text-align: center;
}

.cta-container {
  @include container;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 20px;

  @media (max-width: $tablet) {
    font-size: 2rem;
  }
}

.cta-description {
  color: $text-secondary;
  font-size: 18px;
  margin-bottom: 40px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

// Footer
.footer {
  background: $darker-bg;
  padding: 40px 0 20px;
  border-top: 1px solid $border-color;
}

.footer-container {
  @include container;
}

.footer-content {
  @include flex-between;
  margin-bottom: 30px;

  @media (max-width: $tablet) {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
}

.footer-links {
  display: flex;
  gap: 30px;

  @media (max-width: $mobile) {
    flex-direction: column;
    gap: 15px;
  }
}

.footer-link {
  color: $text-secondary;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;

  &:hover {
    color: $primary-color;
  }
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid $border-color;

  p {
    color: $text-muted;
    font-size: 14px;
  }
}

// Responsive Utilities
@media (max-width: $mobile) {
  .hero-title {
    font-size: 2rem;
  }

  .feature-title,
  .service-title,
  .section-title {
    font-size: 1.75rem;
  }

  .hero-subtitle,
  .feature-description,
  .service-description,
  .results-description {
    font-size: 16px;
  }
}
